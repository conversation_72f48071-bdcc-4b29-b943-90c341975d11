<?php
// Simple responsive test page to verify breakpoints
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Dashboard Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    
    <!-- Google Fonts - Inter -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    
    <!-- Our Responsive CSS -->
    <link rel="stylesheet" href="assets/css/responsive-dashboard.css">
    
    <style>
        body {
            background: var(--bg-tertiary);
            font-family: 'Inter', sans-serif;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
        }
        
        .breakpoint-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 9999;
        }
        
        .device-info {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .test-grid {
            display: grid;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .test-card {
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            text-align: center;
        }
        
        /* Mobile: 1 column */
        @media (max-width: 479px) {
            .test-grid { grid-template-columns: 1fr; }
            .breakpoint-indicator::after { content: " - Mobile"; }
        }
        
        /* Large Mobile: 2 columns */
        @media (min-width: 480px) and (max-width: 767px) {
            .test-grid { grid-template-columns: repeat(2, 1fr); }
            .breakpoint-indicator::after { content: " - Large Mobile"; }
        }
        
        /* Tablet: 2 columns */
        @media (min-width: 768px) and (max-width: 1023px) {
            .test-grid { grid-template-columns: repeat(2, 1fr); }
            .breakpoint-indicator::after { content: " - Tablet"; }
        }
        
        /* Desktop: 4 columns */
        @media (min-width: 1024px) and (max-width: 1439px) {
            .test-grid { grid-template-columns: repeat(4, 1fr); }
            .breakpoint-indicator::after { content: " - Desktop"; }
        }
        
        /* Large Desktop: 4 columns */
        @media (min-width: 1440px) {
            .test-grid { grid-template-columns: repeat(4, 1fr); }
            .breakpoint-indicator::after { content: " - Large Desktop"; }
        }
    </style>
</head>
<body>
    <div class="breakpoint-indicator">
        <span id="screen-width"></span>px
    </div>
    
    <div class="container-fluid">
        <h1 class="text-center mb-4">Responsive Dashboard Test</h1>
        
        <!-- Device Information -->
        <div class="test-section">
            <h2>Device Information</h2>
            <div class="device-info">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Screen Width:</strong> <span id="width-display"></span>px<br>
                        <strong>Screen Height:</strong> <span id="height-display"></span>px<br>
                        <strong>Device Pixel Ratio:</strong> <span id="dpr-display"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>User Agent:</strong> <span id="ua-display"></span><br>
                        <strong>Viewport:</strong> <span id="viewport-display"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Dashboard Grid Test -->
        <div class="test-section">
            <h2>Dashboard Grid Test</h2>
            <p>This grid should show:</p>
            <ul>
                <li><strong>Mobile (< 480px):</strong> 1 column</li>
                <li><strong>Large Mobile (480-767px):</strong> 2 columns</li>
                <li><strong>Tablet (768-1023px):</strong> 2 columns</li>
                <li><strong>Desktop (1024-1439px):</strong> 4 columns</li>
                <li><strong>Large Desktop (≥ 1440px):</strong> 4 columns</li>
            </ul>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="d-flex align-items-center gap-3">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <div class="dashboard-card-title">Total Users</div>
                            <div class="dashboard-card-value">1,234</div>
                        </div>
                    </div>
                </div>
                <div class="dashboard-card">
                    <div class="d-flex align-items-center gap-3">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div>
                            <div class="dashboard-card-title">Active Users</div>
                            <div class="dashboard-card-value">567</div>
                        </div>
                    </div>
                </div>
                <div class="dashboard-card">
                    <div class="d-flex align-items-center gap-3">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div>
                            <div class="dashboard-card-title">Total Courses</div>
                            <div class="dashboard-card-value">89</div>
                        </div>
                    </div>
                </div>
                <div class="dashboard-card">
                    <div class="d-flex align-items-center gap-3">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div>
                            <div class="dashboard-card-title">Total Workouts</div>
                            <div class="dashboard-card-value">456</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Grid -->
        <div class="test-section">
            <h2>Responsive Grid Test</h2>
            <div class="test-grid">
                <div class="test-card">Card 1</div>
                <div class="test-card">Card 2</div>
                <div class="test-card">Card 3</div>
                <div class="test-card">Card 4</div>
                <div class="test-card">Card 5</div>
                <div class="test-card">Card 6</div>
                <div class="test-card">Card 7</div>
                <div class="test-card">Card 8</div>
            </div>
        </div>
        
        <!-- Typography Test -->
        <div class="test-section">
            <h2>Typography Scale Test</h2>
            <div class="row">
                <div class="col-md-6">
                    <h1>Heading 1</h1>
                    <h2>Heading 2</h2>
                    <h3>Heading 3</h3>
                    <h4>Heading 4</h4>
                    <h5>Heading 5</h5>
                    <h6>Heading 6</h6>
                </div>
                <div class="col-md-6">
                    <p style="font-size: var(--font-xs)">Extra Small Text (12px)</p>
                    <p style="font-size: var(--font-sm)">Small Text (14px)</p>
                    <p style="font-size: var(--font-base)">Base Text (16px)</p>
                    <p style="font-size: var(--font-lg)">Large Text (18px)</p>
                    <p style="font-size: var(--font-xl)">Extra Large Text (20px)</p>
                    <p style="font-size: var(--font-2xl)">2X Large Text (24px)</p>
                </div>
            </div>
        </div>
        
        <!-- Spacing Test -->
        <div class="test-section">
            <h2>Spacing Scale Test</h2>
            <div class="row">
                <div class="col-md-6">
                    <div style="background: #f0f0f0; padding: var(--spacing-xs); margin-bottom: 8px;">XS Padding (4px)</div>
                    <div style="background: #f0f0f0; padding: var(--spacing-sm); margin-bottom: 8px;">SM Padding (8px)</div>
                    <div style="background: #f0f0f0; padding: var(--spacing-md); margin-bottom: 8px;">MD Padding (16px)</div>
                    <div style="background: #f0f0f0; padding: var(--spacing-lg); margin-bottom: 8px;">LG Padding (24px)</div>
                    <div style="background: #f0f0f0; padding: var(--spacing-xl); margin-bottom: 8px;">XL Padding (32px)</div>
                    <div style="background: #f0f0f0; padding: var(--spacing-xxl); margin-bottom: 8px;">XXL Padding (48px)</div>
                </div>
            </div>
        </div>
        
        <!-- Button Test -->
        <div class="test-section">
            <h2>Button Test</h2>
            <div class="d-flex flex-wrap gap-2">
                <button class="btn btn-primary">Primary Button</button>
                <button class="btn btn-success">Success Button</button>
                <button class="btn btn-secondary">Secondary Button</button>
                <button class="btn btn-outline-primary">Outline Primary</button>
            </div>
        </div>
        
        <!-- Form Test -->
        <div class="test-section">
            <h2>Form Test</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Text Input</label>
                        <input type="text" class="form-control" placeholder="Enter text">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select</label>
                        <select class="form-select">
                            <option>Option 1</option>
                            <option>Option 2</option>
                            <option>Option 3</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Textarea</label>
                        <textarea class="form-control" rows="3" placeholder="Enter text"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Mobile Bottom Navigation Test -->
    <div class="mobile-bottom-nav d-md-none">
        <a href="#" class="mobile-nav-item active">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="#" class="mobile-nav-item">
            <i class="fas fa-users"></i>
            <span>Users</span>
        </a>
        <a href="#" class="mobile-nav-item">
            <i class="fas fa-book"></i>
            <span>Courses</span>
        </a>
        <a href="#" class="mobile-nav-item">
            <i class="fas fa-chart-line"></i>
            <span>Analytics</span>
        </a>
        <a href="#" class="mobile-nav-item">
            <i class="fas fa-cog"></i>
            <span>Settings</span>
        </a>
    </div>
    
    <script>
        function updateDeviceInfo() {
            document.getElementById('screen-width').textContent = window.innerWidth;
            document.getElementById('width-display').textContent = window.innerWidth;
            document.getElementById('height-display').textContent = window.innerHeight;
            document.getElementById('dpr-display').textContent = window.devicePixelRatio;
            document.getElementById('ua-display').textContent = navigator.userAgent.substring(0, 50) + '...';
            document.getElementById('viewport-display').textContent = `${window.innerWidth} x ${window.innerHeight}`;
        }
        
        // Update on load and resize
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        
        // Update every second to show real-time changes
        setInterval(updateDeviceInfo, 1000);
    </script>
</body>
</html>
