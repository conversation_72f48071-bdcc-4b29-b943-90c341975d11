<?php
// Database configuration
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_USER')) {
    // Use local database for testing, fallback to remote for production
    define('DB_USER', file_exists('/tmp/local_dev') ? 'root' : 'myclo4dz_userkft');
}
if (!defined('DB_PASS')) {
    // Use empty password for local testing, remote password for production
    define('DB_PASS', file_exists('/tmp/local_dev') ? '' : 'HUCbnhvWAO$%');
}
if (!defined('DB_NAME')) {
    // Use local database name for testing
    define('DB_NAME', file_exists('/tmp/local_dev') ? 'kft_fitness' : 'myclo4dz_databaseforge');
}

// Application configuration
define('APP_NAME', 'KFT Fitness Admin');
if (!defined('APP_URL')) {
    define('APP_URL', 'https://mycloudforge.com/admin');
}
define('APP_VERSION', '1.0.0');
// JWT secret for token generation
if (!defined('APP_SECRET')) {
    define('APP_SECRET', 'kft_fitness_jwt_secret_key_2025');
}

// Session configuration
if (!defined('SESSION_NAME')) {
    define('SESSION_NAME', 'kft_session');
}
if (!defined('SESSION_LIFETIME')) {
define('SESSION_LIFETIME', 86400); // 24 hours
}

// Development mode
define('DEV_MODE', true);

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/settings.php';

// Initialize settings
$settingsManager = Settings::getInstance();

// Check if development mode is enabled
$isDevMode = $settingsManager->isEnabled('is_dev_mode', false);

// Error reporting based on environment
if ($isDevMode) {
    // Development mode: Show all errors
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);

    // Define development mode constant
    if (!defined('DEV_MODE')) {
        define('DEV_MODE', true);
    }
} else {
    // Production mode: Hide errors
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);

    // Define development mode constant
    if (!defined('DEV_MODE')) {
        define('DEV_MODE', false);
    }
}

// Time zone
date_default_timezone_set('UTC');
