<?php
/**
 * Settings Class
 *
 * Manages application settings stored in the database
 */
class Settings {
    private $db;
    private $conn;
    private static $instance = null;
    private $settings = [];

    /**
     * Constructor
     */
    private function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
        $this->loadSettings();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new Settings();
        }
        return self::$instance;
    }

    /**
     * Load all settings from database
     */
    private function loadSettings() {
        try {
            $query = "SELECT `key`, `value` FROM settings";
            $result = $this->conn->query($query);

            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $this->settings[$row['key']] = $row['value'];
                }
            }
        } catch (mysqli_sql_exception $e) {
            // Settings table doesn't exist, use defaults
            error_log("Settings table not found, using defaults: " . $e->getMessage());
            $this->settings = [
                'is_dev_mode' => 'true',
                'app_name' => 'KFT Fitness Admin',
                'maintenance_mode' => 'false'
            ];
        }
    }

    /**
     * Get a setting value
     *
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function get($key, $default = null) {
        return isset($this->settings[$key]) ? $this->settings[$key] : $default;
    }

    /**
     * Check if a setting is true (boolean)
     *
     * @param string $key Setting key
     * @param bool $default Default value if setting not found
     * @return bool Whether setting is true
     */
    public function isEnabled($key, $default = false) {
        $value = $this->get($key, $default ? 'true' : 'false');
        return in_array(strtolower($value), ['true', '1', 'yes', 'on', 'enabled']);
    }

    /**
     * Set a setting value
     *
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool Whether setting was updated successfully
     */
    public function set($key, $value) {
        $query = "INSERT INTO settings (`key`, `value`, `is_active`) VALUES (?, ?, 1)
                 ON DUPLICATE KEY UPDATE `value` = ?, `is_active` = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("sss", $key, $value, $value);

        if ($stmt->execute()) {
            $this->settings[$key] = $value;
            $this->loadSettings(); // Reload all settings from DB to keep cache in sync
            return true;
        }

        return false;
    }

    /**
     * Get all settings
     *
     * @param bool $includePrivate Whether to include private settings
     * @return array All settings
     */
    public function getAll($includePrivate = false) {
        $query = "SELECT `key` as setting_key, `value` as setting_value, `group` as setting_description,
                 is_active as is_public, updated_at
                 FROM settings";

        if (!$includePrivate) {
            $query .= " WHERE is_active = 1";
        }

        $result = $this->conn->query($query);
        $settings = [];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $settings[] = $row;
            }
        }

        return $settings;
    }

    /**
     * Toggle a boolean setting
     *
     * @param string $key Setting key
     * @return bool New setting value
     */
    public function toggle($key) {
        $currentValue = $this->isEnabled($key);
        $newValue = $currentValue ? 'false' : 'true';

        if ($this->set($key, $newValue)) {
            return !$currentValue;
        }

        return $currentValue;
    }
}
