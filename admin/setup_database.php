<?php
// Database setup script to create missing tables for local development
require_once 'includes/config.php';

// Create database if it doesn't exist
$conn_no_db = new mysqli(DB_HOST, DB_USER, DB_PASS);
if ($conn_no_db->connect_error) {
    die("Connection failed: " . $conn_no_db->connect_error);
}

$sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
if ($conn_no_db->query($sql) === TRUE) {
    echo "Database created successfully or already exists<br>";
} else {
    echo "Error creating database: " . $conn_no_db->error . "<br>";
}
$conn_no_db->close();

// Now connect to the database
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create settings table
$sql = "CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(255) NOT NULL UNIQUE,
    `value` TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Settings table created successfully<br>";
    
    // Insert default settings
    $default_settings = [
        ['is_dev_mode', 'true'],
        ['app_name', 'KFT Fitness Admin'],
        ['maintenance_mode', 'false'],
        ['max_upload_size', '10MB'],
        ['timezone', 'UTC']
    ];
    
    foreach ($default_settings as $setting) {
        $stmt = $conn->prepare("INSERT IGNORE INTO settings (`key`, `value`) VALUES (?, ?)");
        $stmt->bind_param("ss", $setting[0], $setting[1]);
        $stmt->execute();
    }
    echo "Default settings inserted<br>";
} else {
    echo "Error creating settings table: " . $conn->error . "<br>";
}

// Create admin_users table
$sql = "CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    role ENUM('super_admin', 'admin', 'staff') DEFAULT 'staff',
    is_active BOOLEAN DEFAULT TRUE,
    parent_admin_id INT NULL,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_admin_id) REFERENCES admin_users(id) ON DELETE SET NULL
)";

if ($conn->query($sql) === TRUE) {
    echo "Admin users table created successfully<br>";
    
    // Insert default admin user
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $conn->prepare("INSERT IGNORE INTO admin_users (username, email, password, first_name, last_name, role) VALUES (?, ?, ?, ?, ?, ?)");
    $username = 'admin';
    $email = '<EMAIL>';
    $first_name = 'Admin';
    $last_name = 'User';
    $role = 'super_admin';
    $stmt->bind_param("ssssss", $username, $email, $password, $first_name, $last_name, $role);
    $stmt->execute();
    echo "Default admin user created (username: admin, password: admin123)<br>";
} else {
    echo "Error creating admin_users table: " . $conn->error . "<br>";
}

// Create users table
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    phone VARCHAR(20),
    phone_number VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    profile_image VARCHAR(255),
    last_login TIMESTAMP NULL,
    assigned_staff_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Users table created successfully<br>";
    
    // Insert sample users
    $sample_users = [
        ['john_doe', '<EMAIL>', 'John', 'Doe', '+1234567890', '1990-01-15', 'male'],
        ['jane_smith', '<EMAIL>', 'Jane', 'Smith', '+1234567891', '1992-03-22', 'female'],
        ['mike_johnson', '<EMAIL>', 'Mike', 'Johnson', '+1234567892', '1988-07-10', 'male'],
        ['sarah_wilson', '<EMAIL>', 'Sarah', 'Wilson', '+1234567893', '1995-11-05', 'female'],
        ['david_brown', '<EMAIL>', 'David', 'Brown', '+1234567894', '1987-09-18', 'male']
    ];
    
    foreach ($sample_users as $user) {
        $password = password_hash('password123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT IGNORE INTO users (username, email, password, first_name, last_name, phone, date_of_birth, gender) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssssss", $user[0], $user[1], $password, $user[2], $user[3], $user[4], $user[5], $user[6]);
        $stmt->execute();
    }
    echo "Sample users created<br>";
} else {
    echo "Error creating users table: " . $conn->error . "<br>";
}

// Create courses table
$sql = "CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor VARCHAR(100),
    duration_weeks INT DEFAULT 1,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    max_participants INT DEFAULT 20,
    price DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Courses table created successfully<br>";
    
    // Insert sample courses
    $sample_courses = [
        ['Beginner Fitness Bootcamp', 'A comprehensive fitness program for beginners', 'John Trainer', 4, 'beginner', 15, 99.99],
        ['Advanced Strength Training', 'High-intensity strength training for experienced athletes', 'Sarah Coach', 8, 'advanced', 10, 199.99],
        ['Yoga for Flexibility', 'Improve flexibility and mindfulness through yoga', 'Emma Yogi', 6, 'beginner', 20, 79.99],
        ['HIIT Cardio Challenge', 'High-intensity interval training for maximum results', 'Mike Trainer', 4, 'intermediate', 12, 129.99]
    ];
    
    foreach ($sample_courses as $course) {
        $stmt = $conn->prepare("INSERT IGNORE INTO courses (title, description, instructor, duration_weeks, difficulty_level, max_participants, price) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sssisid", $course[0], $course[1], $course[2], $course[3], $course[4], $course[5], $course[6]);
        $stmt->execute();
    }
    echo "Sample courses created<br>";
} else {
    echo "Error creating courses table: " . $conn->error . "<br>";
}

// Create workouts table
$sql = "CREATE TABLE IF NOT EXISTS workouts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    course_id INT,
    workout_name VARCHAR(255) NOT NULL,
    workout_type ENUM('cardio', 'strength', 'flexibility', 'mixed') DEFAULT 'mixed',
    duration_minutes INT DEFAULT 30,
    calories_burned INT DEFAULT 0,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    completed_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL
)";

if ($conn->query($sql) === TRUE) {
    echo "Workouts table created successfully<br>";
    
    // Insert sample workouts
    for ($i = 1; $i <= 20; $i++) {
        $user_id = rand(1, 5);
        $course_id = rand(1, 4);
        $workout_types = ['cardio', 'strength', 'flexibility', 'mixed'];
        $workout_type = $workout_types[array_rand($workout_types)];
        $duration = rand(20, 90);
        $calories = rand(150, 500);
        
        $stmt = $conn->prepare("INSERT INTO workouts (user_id, course_id, workout_name, workout_type, duration_minutes, calories_burned, completed_at) VALUES (?, ?, ?, ?, ?, ?, NOW() - INTERVAL ? DAY)");
        $workout_name = "Workout Session #" . $i;
        $days_ago = rand(0, 30);
        $stmt->bind_param("iissiii", $user_id, $course_id, $workout_name, $workout_type, $duration, $calories, $days_ago);
        $stmt->execute();
    }
    echo "Sample workouts created<br>";
} else {
    echo "Error creating workouts table: " . $conn->error . "<br>";
}

// Create workout_records table for reports
$sql = "CREATE TABLE IF NOT EXISTS workout_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    workout_id INT,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    duration_minutes INT DEFAULT 0,
    calories_burned INT DEFAULT 0,
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (workout_id) REFERENCES workouts(id) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "Workout records table created successfully<br>";

    // Insert sample workout records
    for ($i = 1; $i <= 50; $i++) {
        $user_id = rand(1, 5);
        $workout_id = rand(1, 20);
        $duration = rand(20, 90);
        $calories = rand(150, 500);
        $days_ago = rand(0, 30);

        $stmt = $conn->prepare("INSERT INTO workout_records (user_id, workout_id, duration_minutes, calories_burned, recorded_at) VALUES (?, ?, ?, ?, NOW() - INTERVAL ? DAY)");
        $stmt->bind_param("iiiii", $user_id, $workout_id, $duration, $calories, $days_ago);
        $stmt->execute();
    }
    echo "Sample workout records created<br>";
} else {
    echo "Error creating workout_records table: " . $conn->error . "<br>";
}

// Add missing columns if they don't exist
echo "<h3>Adding missing columns...</h3>";

// Add is_premium column to users table if it doesn't exist
$sql = "SHOW COLUMNS FROM users LIKE 'is_premium'";
$result = $conn->query($sql);
if ($result->num_rows == 0) {
    $sql = "ALTER TABLE users ADD COLUMN is_premium BOOLEAN DEFAULT FALSE";
    if ($conn->query($sql) === TRUE) {
        echo "✅ Added is_premium column to users table<br>";
    } else {
        echo "❌ Error adding is_premium column: " . $conn->error . "<br>";
    }
} else {
    echo "✅ is_premium column already exists in users table<br>";
}

// Add parent_admin_id column to admin_users table if it doesn't exist
$sql = "SHOW COLUMNS FROM admin_users LIKE 'parent_admin_id'";
$result = $conn->query($sql);
if ($result->num_rows == 0) {
    $sql = "ALTER TABLE admin_users ADD COLUMN parent_admin_id INT NULL";
    if ($conn->query($sql) === TRUE) {
        echo "✅ Added parent_admin_id column to admin_users table<br>";
    } else {
        echo "❌ Error adding parent_admin_id column: " . $conn->error . "<br>";
    }
} else {
    echo "✅ parent_admin_id column already exists in admin_users table<br>";
}

// Add phone_number column to users table if it doesn't exist
$sql = "SHOW COLUMNS FROM users LIKE 'phone_number'";
$result = $conn->query($sql);
if ($result->num_rows == 0) {
    $sql = "ALTER TABLE users ADD COLUMN phone_number VARCHAR(20)";
    if ($conn->query($sql) === TRUE) {
        echo "✅ Added phone_number column to users table<br>";
    } else {
        echo "❌ Error adding phone_number column: " . $conn->error . "<br>";
    }
} else {
    echo "✅ phone_number column already exists in users table<br>";
}

// Add last_login column to users table if it doesn't exist
$sql = "SHOW COLUMNS FROM users LIKE 'last_login'";
$result = $conn->query($sql);
if ($result->num_rows == 0) {
    $sql = "ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL";
    if ($conn->query($sql) === TRUE) {
        echo "✅ Added last_login column to users table<br>";
    } else {
        echo "❌ Error adding last_login column: " . $conn->error . "<br>";
    }
} else {
    echo "✅ last_login column already exists in users table<br>";
}

// Add assigned_staff_id column to users table if it doesn't exist
$sql = "SHOW COLUMNS FROM users LIKE 'assigned_staff_id'";
$result = $conn->query($sql);
if ($result->num_rows == 0) {
    $sql = "ALTER TABLE users ADD COLUMN assigned_staff_id INT";
    if ($conn->query($sql) === TRUE) {
        echo "✅ Added assigned_staff_id column to users table<br>";
    } else {
        echo "❌ Error adding assigned_staff_id column: " . $conn->error . "<br>";
    }
} else {
    echo "✅ assigned_staff_id column already exists in users table<br>";
}

$conn->close();

echo "<br><strong>Database setup completed successfully!</strong><br>";
echo "<a href='index.php'>Go to Dashboard</a>";
?>
