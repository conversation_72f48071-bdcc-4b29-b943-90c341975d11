<?php
// Database verification script
require_once 'includes/config.php';

// Connect to the database
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Database Verification</h1>";

// Check if database exists
$result = $conn->query("SELECT DATABASE()");
$row = $result->fetch_row();
echo "<h2>Current Database: " . $row[0] . "</h2>";

// List all tables
echo "<h2>Tables in Database:</h2>";
$result = $conn->query("SHOW TABLES");
if ($result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_row()) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No tables found in database.</p>";
}

// Check specific table structures
$tables_to_check = ['settings', 'admin_users', 'users', 'courses', 'workouts', 'workout_records'];

foreach ($tables_to_check as $table) {
    echo "<h3>Table: $table</h3>";
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table exists</p>";
        
        // Show table structure
        $result = $conn->query("DESCRIBE $table");
        if ($result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Show row count
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        $row = $result->fetch_assoc();
        echo "<p>Row count: " . $row['count'] . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Table does not exist</p>";
    }
}

$conn->close();

echo "<br><a href='index.php'>Go to Dashboard</a>";
?>
