<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isSuperAdmin = $auth->hasRole('super_admin');
$isAdmin = $auth->hasRole('admin') || $isSuperAdmin;
$isStaff = $auth->hasRole('staff');

// Get all staff members for the filter dropdown (for super admins)
$staffMembers = [];
if ($isSuperAdmin) {
    $staffMembers = getAllStaffMembers($conn);
}

// Get selected staff ID from query parameter (for super admins)
$staffId = isset($_GET['staff_id']) ? (int)$_GET['staff_id'] : '';

// Create filters array for the helper function
$filters = [
    'staff_id' => $staffId
];

// Get user statistics based on filters and current admin role
$userStats = getUserStatistics($conn, $auth, $filters);
$totalUsers = $userStats['total'];
$activeUsers = $userStats['active'];

// Get total workouts count
$workoutsQuery = "SELECT COUNT(*) as total FROM workout_records";
$workoutsResult = $conn->query($workoutsQuery);
$totalWorkouts = $workoutsResult->fetch_assoc()['total'] ?? 0;

// Get total courses count
$coursesQuery = "SELECT COUNT(*) as total FROM courses";
$coursesResult = $conn->query($coursesQuery);
$totalCourses = $coursesResult->fetch_assoc()['total'] ?? 0;

// Get average BMI
$bmiQuery = "SELECT AVG(bmi) as avg_bmi FROM bmi_records";
$bmiResult = $conn->query($bmiQuery);
$avgBmi = $bmiResult->fetch_assoc()['avg_bmi'] ?? 0;
$avgBmi = number_format($avgBmi, 2);

// Get recent users
$recentUsersQuery = "SELECT * FROM users WHERE 1=1";

// Apply role-based filtering
if ($currentAdminRole === 'staff') {
    // Staff can only see users assigned to them
    $recentUsersQuery .= " AND assigned_staff_id = $currentAdminId";
} elseif ($isSuperAdmin && !empty($staffId)) {
    // Super admin filtering by staff
    $recentUsersQuery .= " AND assigned_staff_id = $staffId";
}

$recentUsersQuery .= " ORDER BY created_at DESC LIMIT 5";
$recentUsersResult = $conn->query($recentUsersQuery);

// Get workout data for chart (last 7 days)
$workoutChartQuery = "SELECT DATE(recorded_at) as workout_date,
                      COUNT(*) as workout_count
                      FROM workout_records
                      WHERE recorded_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                      GROUP BY DATE(recorded_at)
                      ORDER BY workout_date";
$workoutChartResult = $conn->query($workoutChartQuery);

$workoutDates = [];
$workoutCounts = [];

if ($workoutChartResult && $workoutChartResult->num_rows > 0) {
    while ($row = $workoutChartResult->fetch_assoc()) {
        $workoutDates[] = date('M d', strtotime($row['workout_date']));
        $workoutCounts[] = $row['workout_count'];
    }
}

// Get staff statistics (for super admins)
$staffStats = [];
$overallStaffMetrics = [];
if ($isSuperAdmin) {
    try {
        // Get overall staff metrics
        $overallStaffMetrics = getOverallStaffMetrics($conn);

        // Get individual staff statistics
        $staffStats = getStaffStatistics($conn);
    } catch (Exception $e) {
        // Log the error but continue execution
        error_log("Error getting staff statistics: " . $e->getMessage());
        $staffStats = [];
        $overallStaffMetrics = [
            'total_staff' => 0,
            'total_assigned_users' => 0,
            'avg_users_per_staff' => 0,
            'top_staff' => null,
            'top_engagement_staff' => null
        ];
    }
}

// Add CSS for new dashboard design
echo '<link rel="stylesheet" href="assets/css/dashboard-new.css">';
echo '<link rel="stylesheet" href="assets/css/responsive-dashboard.css">';
?>

<!-- Dashboard Header -->
<div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4 gap-3">
    <div>
        <h1 class="h3 font-weight-bold text-dark">My Dashboard</h1>
        <p class="text-muted">
            Hello, <?php echo isset($_SESSION['name']) ? htmlspecialchars($_SESSION['name']) : (isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'User'); ?>!
            <?php if ($isStaff): ?>
                Here's an overview of your assigned users.
            <?php elseif ($isSuperAdmin && !empty($staffId)): ?>
                Viewing data for users assigned to selected staff.
            <?php else: ?>
                Here's your fitness overview.
            <?php endif; ?>
        </p>
    </div>
    <div class="d-flex flex-column flex-sm-row align-items-stretch align-items-sm-center gap-2">
        <?php if ($isSuperAdmin && !empty($staffMembers)): ?>
        <form method="get" class="me-0 me-sm-3">
            <select class="form-select form-select-sm" name="staff_id" onchange="this.form.submit()">
                <option value="">All Users</option>
                <?php foreach ($staffMembers as $staff): ?>
                <option value="<?php echo $staff['id']; ?>" <?php echo $staffId == $staff['id'] ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($staff['name']); ?>'s Users
                </option>
                <?php endforeach; ?>
            </select>
        </form>
        <?php endif; ?>
        <button class="btn btn-primary shadow-sm w-100 w-sm-auto" onclick="window.location.reload();">
            <i class="fas fa-sync-alt me-2"></i> Refresh Data
        </button>
    </div>
</div>

<!-- Responsive User Stats Cards -->
<div class="dashboard-grid">
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-users"></i>
            </div>
            <div>
                <div class="dashboard-card-title">Total Users</div>
                <div class="dashboard-card-value"><?php echo $totalUsers; ?></div>
            </div>
        </div>
    </div>
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <div>
                <div class="dashboard-card-title">Active Users</div>
                <div class="dashboard-card-value"><?php echo $activeUsers; ?></div>
            </div>
        </div>
    </div>
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div>
                <div class="dashboard-card-title">Total Courses</div>
                <div class="dashboard-card-value"><?php echo $totalCourses; ?></div>
            </div>
        </div>
    </div>
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-medal"></i>
            </div>
            <div>
                <div class="dashboard-card-title">Total Workouts</div>
                <div class="dashboard-card-value"><?php echo $totalWorkouts; ?></div>
            </div>
        </div>
    </div>
</div>

<?php if ($isSuperAdmin): ?>
<!-- Responsive Staff Stats Cards (Super Admin Only) -->
<div class="dashboard-grid">
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <div>
                <div class="dashboard-card-title">Total Staff</div>
                <div class="dashboard-card-value"><?php echo isset($overallStaffMetrics['total_staff']) ? $overallStaffMetrics['total_staff'] : 0; ?></div>
            </div>
        </div>
    </div>
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <div>
                <div class="dashboard-card-title">Assigned Users</div>
                <div class="dashboard-card-value"><?php echo isset($overallStaffMetrics['total_assigned_users']) ? $overallStaffMetrics['total_assigned_users'] : 0; ?></div>
            </div>
        </div>
    </div>
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div>
                <div class="dashboard-card-title">Top Engagement</div>
                <div class="dashboard-card-value"><?php echo !empty($overallStaffMetrics['top_engagement_staff']) ? $overallStaffMetrics['top_engagement_staff']['engagement_rate'] . '%' : '0%'; ?></div>
            </div>
        </div>
    </div>
    <div class="dashboard-card">
        <div class="d-flex align-items-center gap-3">
            <div class="dashboard-card-icon">
                <i class="fas fa-tasks"></i>
            </div>
            <div>
                <div class="dashboard-card-title">New Users (7d)</div>
                <div class="dashboard-card-value"><?php echo !empty($staffStats) ? array_sum(array_column($staffStats, 'new_users')) : 0; ?></div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <?php if ($isSuperAdmin): ?>
        <!-- Removed Staff Completion Rate chart section -->
        <?php endif; ?>
    </div>
    <div class="col-lg-4 mb-4">
        <!-- Removed User Statistics chart section -->
    </div>
</div>

<!-- Tabs and Workout List -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-4">
                <!-- Tabs -->
                <!-- Removed the My workouts tab and its container -->

                <!-- Removed New Users Bar Chart Card -->

                <?php if ($isSuperAdmin): ?>
                <!-- Staff Performance Table (Only for Super Admins) -->
                <div class="card border-0 shadow-sm h-100 modern-staff-metrics">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center sticky-top" style="top:0;z-index:2;">
                        <h5 class="mb-0 font-weight-bold">Staff Performance Metrics</h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshStaffTable">
                                <i class="fas fa-sync-alt me-1"></i> Refresh
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.location.href='staff_management.php'">
                                <i class="fas fa-external-link-alt me-1"></i> Manage Staff
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0 modern-staff-table">
                                <thead class="table-light">
                                    <tr>
                                        <th>Staff</th>
                                        <th class="text-center">Assigned</th>
                                        <th class="text-center">Active</th>
                                        <th class="text-center">New (7d)</th>
                                        <th class="text-center">Completion</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($staffStats)): ?>
                                        <?php foreach ($staffStats as $staff): ?>
                                        <?php
                                        $completionClass = 'border-danger';
                                        if ($staff['completion_rate'] >= 70) {
                                            $completionClass = 'border-success';
                                        } elseif ($staff['completion_rate'] >= 40) {
                                            $completionClass = 'border-warning';
                                        } elseif ($staff['completion_rate'] >= 20) {
                                            $completionClass = 'border-info';
                                        }
                                        ?>
                                        <tr class="modern-staff-row <?php echo $completionClass; ?>">
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="avatar-circle bg-light text-dark border shadow-sm" style="font-size:1.1rem;width:38px;height:38px;">
                                                        <?php echo strtoupper(substr($staff['username'], 0, 1)); ?>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold text-dark"><?php echo htmlspecialchars($staff['name']); ?></div>
                                                        <div class="small text-muted">@<?php echo htmlspecialchars($staff['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-light text-dark fw-bold px-3 py-2 shadow-sm"><?php echo $staff['assigned_users']; ?></span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success-light text-success fw-bold px-3 py-2 shadow-sm"><?php echo $staff['active_users']; ?></span>
                                                <div class="progress mt-1" style="height: 5px; width: 60px; margin: 0 auto;">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $staff['active_percentage']; ?>%" aria-valuenow="<?php echo $staff['active_percentage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info-light text-info fw-bold px-3 py-2 shadow-sm"><?php echo $staff['new_users']; ?></span>
                                                <div class="small text-muted mt-1"><?php echo $staff['assigned_users'] > 0 ? round(($staff['new_users'] / $staff['assigned_users']) * 100, 1) : 0; ?>%</div>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge px-3 py-2 fw-bold shadow-sm <?php echo $completionClass; ?>" style="background:#f8f9fa;color:#111;">
                                                    <?php echo $staff['completion_rate']; ?>%
                                                </span>
                                                <div class="progress mt-1" style="height: 8px; width: 60px; margin: 0 auto;">
                                                    <div class="progress-bar <?php echo $completionClass; ?>" role="progressbar" style="width: <?php echo $staff['completion_rate']; ?>%" aria-valuenow="<?php echo $staff['completion_rate']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group">
                                                    <a href="admin_edit.php?id=<?php echo $staff['id']; ?>" class="btn btn-sm btn-success" title="Edit staff">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <div class="py-3">
                                                    <i class="fas fa-users fa-2x text-muted mb-3"></i>
                                                    <p class="mb-0">No staff members found. Staff Management module shows <?php echo isset($overallStaffMetrics['total_staff']) ? $overallStaffMetrics['total_staff'] : 0; ?> staff members.</p>
                                                    <?php if (isset($overallStaffMetrics['total_staff']) && $overallStaffMetrics['total_staff'] > 0): ?>
                                                        <p class="text-muted small">There may be an issue with staff data retrieval. Please check if staff members are active.</p>
                                                    <?php endif; ?>
                                                    <a href="admin_add.php" class="btn btn-sm btn-primary mt-3">
                                                        <i class="fas fa-user-plus me-2"></i> Add Staff Member
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-white py-2">
                        <div class="small text-muted">
                            Showing <?php echo count($staffStats); ?> staff members
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Workout List -->
                <!-- Removed the workout list table and its markup -->
            </div>
        </div>
    </div>
</div>

<!-- Mobile Bottom Navigation -->
<div class="mobile-bottom-nav d-md-none">
    <a href="index.php" class="mobile-nav-item active">
        <div class="mobile-nav-icon">
            <i class="fas fa-home"></i>
        </div>
        <div class="mobile-nav-label">Dashboard</div>
    </a>
    <a href="users.php" class="mobile-nav-item">
        <div class="mobile-nav-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="mobile-nav-label">Users</div>
    </a>
    <a href="courses.php" class="mobile-nav-item">
        <div class="mobile-nav-icon">
            <i class="fas fa-graduation-cap"></i>
        </div>
        <div class="mobile-nav-label">Courses</div>
    </a>
    <a href="reports.php" class="mobile-nav-item">
        <div class="mobile-nav-icon">
            <i class="fas fa-chart-bar"></i>
        </div>
        <div class="mobile-nav-label">Reports</div>
    </a>
    <a href="settings.php" class="mobile-nav-item">
        <div class="mobile-nav-icon">
            <i class="fas fa-cog"></i>
        </div>
        <div class="mobile-nav-label">Settings</div>
    </a>
</div>

<style>
/* User Avatar Styles */
.user-avatar-sm {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Workout Icon Styles */
.workout-icon-sm {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

/* Minimalist Card Styles */
.minimalist-card {
    background: #fff !important;
    border: 1px solid #eee !important;
    box-shadow: none !important;
    padding: 1.25rem 1rem !important;
    min-height: 90px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.minimalist-icon {
    font-size: 1.5rem;
    color: #111 !important;
    background: none !important;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
}
.minimalist-label {
    font-size: 0.85rem;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    margin-bottom: 0.15rem;
}
.minimalist-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111;
    line-height: 1.1;
}

.dashboard-green-btn, .btn-success {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.dashboard-green-btn:hover, .btn-success:hover {
    background-color: #219150 !important;
    color: #fff !important;
}

/* Modern Staff Metrics Section */
.modern-staff-metrics {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 24px rgba(0,0,0,0.06);
    margin-bottom: 2rem;
}
.modern-staff-table thead th {
    background: #f8f9fa;
    font-size: 0.92rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e0e0e0;
}
.modern-staff-table tbody tr {
    transition: background 0.18s;
}
.modern-staff-table tbody tr:hover {
    background: #f4f8f6;
}
.modern-staff-row.border-success {
    border-left: 5px solid #27ae60;
}
.modern-staff-row.border-warning {
    border-left: 5px solid #f4d35e;
}
.modern-staff-row.border-info {
    border-left: 5px solid #27ae60;
}
.modern-staff-row.border-danger {
    border-left: 5px solid #e74c3c;
}
.avatar-circle {
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    background: #f0f0f0;
}
</style>

<script>
// Workout Activity Chart
document.addEventListener('DOMContentLoaded', function() {
    // Set Chart.js defaults
    Chart.defaults.font.family = "'Inter', sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#7e8299';

    <?php if ($isSuperAdmin): ?>
    // Staff Activity Chart - Real Time
    var staffActivityChart = null;
    function updateStaffActivityChart(data) {
        var staffNames = data.map(function(staff) { return staff.name; });
        var completionRates = data.map(function(staff) { return staff.completion_rate; });
        var ctx = document.getElementById('staffActivityChart').getContext('2d');
        if (staffActivityChart) {
            staffActivityChart.data.labels = staffNames;
            staffActivityChart.data.datasets[0].data = completionRates;
            staffActivityChart.update();
        } else {
            staffActivityChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: staffNames,
                    datasets: [{
                        label: 'Completion Rate (%)',
                        data: completionRates,
                        backgroundColor: 'var(--primary)',
                        borderRadius: 6,
                        maxBarThickness: 28
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: '#fff',
                            titleColor: '#111',
                            bodyColor: '#222',
                            borderColor: '#eee',
                            borderWidth: 1,
                            padding: 12,
                            boxPadding: 6,
                            usePointStyle: true
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { precision: 0, padding: 10, color: '#222', callback: function(value) { return value + '%'; } },
                            grid: { color: 'rgba(0,0,0,0.08)', drawBorder: false }
                        },
                        y: {
                            grid: { display: false, drawBorder: false },
                            ticks: { padding: 10, color: '#222' }
                        }
                    }
                }
            });
        }
    }
    function fetchStaffActivityChartData() {
        fetch('ajax_staff_stats.php')
            .then(function(response) { return response.json(); })
            .then(function(data) { updateStaffActivityChart(data); });
    }
    // Initial load
    fetchStaffActivityChartData();
    // Refresh every 60 seconds
    setInterval(fetchStaffActivityChartData, 60000);
    // Manual refresh
    document.getElementById('refreshStaffActivityChart').addEventListener('click', function() {
        this.disabled = true;
        var icon = this.querySelector('i');
        if (icon) icon.classList.add('spinning');
        fetchStaffActivityChartData();
        setTimeout(() => {
            this.disabled = false;
            if (icon) icon.classList.remove('spinning');
        }, 800);
    });
    <?php endif; ?>
    // User Stats Chart with black/white data
    var userStatsCtx = document.getElementById('userStatsChart').getContext('2d');
    var userStatsChart = new Chart(userStatsCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active', 'Inactive', 'Premium'],
            datasets: [{
                data: [
                    <?php echo $userStats['active_percentage']; ?>,
                    <?php echo 100 - $userStats['active_percentage']; ?>,
                    <?php echo $userStats['premium_percentage']; ?>
                ],
                backgroundColor: ['#111', '#888', '#eee'],
                borderWidth: 0,
                hoverOffset: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        color: '#222'
                    }
                },
                tooltip: {
                    backgroundColor: '#fff',
                    titleColor: '#111',
                    bodyColor: '#222',
                    borderColor: '#eee',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true
                }
            },
            cutout: '75%'
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    // New Users Chart - Real Time
    var newUsersChart = null;
    function updateNewUsersChart(data) {
        var dates = data.dates;
        var counts = data.counts;
        var ctx = document.getElementById('newUsersChart').getContext('2d');
        var chartCanvas = document.getElementById('newUsersChart');
        var noDataMsg = document.getElementById('newUsersChartNoData');
        if (dates.length === 0 || counts.length === 0) {
            chartCanvas.style.display = 'none';
            noDataMsg.style.display = 'block';
            if (newUsersChart) {
                newUsersChart.destroy();
                newUsersChart = null;
            }
            return;
        } else {
            chartCanvas.style.display = 'block';
            noDataMsg.style.display = 'none';
        }
        if (newUsersChart) {
            newUsersChart.data.labels = dates;
            newUsersChart.data.datasets[0].data = counts;
            newUsersChart.update();
        } else {
            newUsersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'New Users',
                        data: counts,
                        backgroundColor: '#111',
                        borderRadius: 6,
                        maxBarThickness: 28
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: '#fff',
                            titleColor: '#111',
                            bodyColor: '#222',
                            borderColor: '#eee',
                            borderWidth: 1,
                            padding: 12,
                            boxPadding: 6,
                            usePointStyle: true
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { precision: 0, padding: 10, color: '#222' },
                            grid: { color: 'rgba(0,0,0,0.08)', drawBorder: false }
                        },
                        x: {
                            grid: { display: false, drawBorder: false },
                            ticks: { padding: 10, color: '#222' }
                        }
                    }
                }
            });
        }
    }
    function fetchNewUsersChartData() {
        fetch('ajax_new_users_chart.php')
            .then(function(response) { return response.json(); })
            .then(function(data) { updateNewUsersChart(data); });
    }
    fetchNewUsersChartData();
    setInterval(fetchNewUsersChartData, 60000);
    document.getElementById('refreshNewUsersChart').addEventListener('click', function() {
        this.disabled = true;
        var icon = this.querySelector('i');
        if (icon) icon.classList.add('spinning');
        fetchNewUsersChartData();
        setTimeout(() => {
            this.disabled = false;
            if (icon) icon.classList.remove('spinning');
        }, 800);
    });

    // Staff table refresh button
    const refreshStaffBtn = document.getElementById('refreshStaffTable');
    if (refreshStaffBtn) {
        refreshStaffBtn.addEventListener('click', function() {
            const icon = this.querySelector('.fa-sync-alt');
            icon.classList.add('spinning');
            this.disabled = true;

            // Simulate refresh delay
            setTimeout(() => {
                location.reload();
            }, 800);
        });
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
