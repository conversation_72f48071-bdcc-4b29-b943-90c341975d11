/* Comprehensive Responsive Dashboard CSS */
/* Mobile-First Approach with Consistent Breakpoints */

:root {
  /* Responsive Breakpoints */
  --mobile-max: 479px;
  --large-mobile-min: 480px;
  --large-mobile-max: 767px;
  --tablet-min: 768px;
  --tablet-max: 1023px;
  --desktop-min: 1024px;
  --desktop-max: 1439px;
  --large-desktop-min: 1440px;

  /* Responsive Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-xxl: 3rem;     /* 48px */

  /* Responsive Typography Scale */
  --font-xs: 0.75rem;      /* 12px */
  --font-sm: 0.875rem;     /* 14px */
  --font-base: 1rem;       /* 16px */
  --font-lg: 1.125rem;     /* 18px */
  --font-xl: 1.25rem;      /* 20px */
  --font-2xl: 1.5rem;      /* 24px */
  --font-3xl: 1.875rem;    /* 30px */
  --font-4xl: 2.25rem;     /* 36px */

  /* Layout Variables */
  --sidebar-width-mobile: 280px;
  --sidebar-width-tablet: 250px;
  --sidebar-width-desktop: 280px;
  --header-height: 60px;
  --bottom-nav-height: 60px;

  /* Colors */
  --primary-color: #111;
  --secondary-color: #666;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #3498db;
  
  --bg-primary: #fff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f5f7fa;
  --text-primary: #111;
  --text-secondary: #666;
  --text-muted: #999;
  --border-color: #e0e0e0;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Dark Mode Variables */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #333;
    --text-primary: #fff;
    --text-secondary: #ccc;
    --text-muted: #999;
    --border-color: #444;
  }
}

/* Base Responsive Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container System */
.container-fluid {
  width: 100%;
  padding: var(--spacing-md);
  margin: 0 auto;
}

/* Mobile: Base styles (0-479px) */
@media (max-width: 479px) {
  .container-fluid {
    padding: var(--spacing-sm);
  }
  
  .dashboard-card {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
  }
  
  .dashboard-card-title {
    font-size: var(--font-sm);
  }
  
  .dashboard-card-value {
    font-size: var(--font-xl);
  }
}

/* Large Mobile: 480px - 767px */
@media (min-width: 480px) and (max-width: 767px) {
  .container-fluid {
    padding: var(--spacing-md);
  }
  
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .dashboard-card {
    padding: var(--spacing-lg);
  }
}

/* Tablet: 768px - 1023px */
@media (min-width: 768px) and (max-width: 1023px) {
  .container-fluid {
    padding: var(--spacing-lg);
  }
  
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
  
  .dashboard-card {
    padding: var(--spacing-lg);
  }
  
  .dashboard-card-title {
    font-size: var(--font-base);
  }
  
  .dashboard-card-value {
    font-size: var(--font-2xl);
  }
}

/* Desktop: 1024px - 1439px */
@media (min-width: 1024px) and (max-width: 1439px) {
  .container-fluid {
    padding: var(--spacing-xl);
  }
  
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xl);
  }
  
  .dashboard-card {
    padding: var(--spacing-xl);
  }
  
  .dashboard-card-title {
    font-size: var(--font-lg);
  }
  
  .dashboard-card-value {
    font-size: var(--font-3xl);
  }
}

/* Large Desktop: 1440px+ */
@media (min-width: 1440px) {
  .container-fluid {
    padding: var(--spacing-xxl);
    max-width: 1600px;
  }
  
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-xxl);
  }
  
  .dashboard-card {
    padding: var(--spacing-xxl);
  }
  
  .dashboard-card-title {
    font-size: var(--font-xl);
  }
  
  .dashboard-card-value {
    font-size: var(--font-4xl);
  }
}

/* Layout Components */
#wrapper {
  display: flex;
  min-height: 100vh;
  position: relative;
  transition: all 0.3s ease;
}

/* Sidebar Responsive Behavior */
#sidebar-wrapper {
  width: var(--sidebar-width-desktop);
  min-height: 100vh;
  background: var(--primary-color);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  transition: transform 0.3s ease;
  overflow-y: auto;
}

/* Page Content */
#page-content-wrapper {
  flex: 1;
  margin-left: var(--sidebar-width-desktop);
  min-height: 100vh;
  background: var(--bg-tertiary);
  transition: margin-left 0.3s ease;
}

/* Top Navigation */
.top-navbar {
  background: var(--bg-primary);
  height: var(--header-height);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 999;
}

/* Dashboard Grid System */
.dashboard-grid {
  display: grid;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

/* Dashboard Cards */
.dashboard-card {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--border-color);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.dashboard-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  background: var(--bg-secondary);
  color: var(--primary-color);
}

.dashboard-card-title {
  font-size: var(--font-sm);
  font-weight: 600;
  text-transform: uppercase;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  letter-spacing: 0.5px;
}

.dashboard-card-value {
  font-size: var(--font-2xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

/* Responsive Tables */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

.table {
  margin-bottom: 0;
  background: var(--bg-primary);
}

.table th {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding: var(--spacing-md);
}

.table td {
  padding: var(--spacing-md);
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

/* Forms */
.form-control, .form-select {
  padding: var(--spacing-md);
  font-size: var(--font-base);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(17, 17, 17, 0.25);
  outline: none;
}

/* Buttons */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-sm);
  font-weight: 500;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #333;
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover {
  background: #219150;
  transform: translateY(-1px);
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-grid { display: grid; }
.d-block { display: block; }
.d-none { display: none; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* Mobile-Specific Responsive Styles */
@media (max-width: 767px) {
  /* Hide sidebar by default on mobile */
  #sidebar-wrapper {
    transform: translateX(-100%);
    visibility: hidden;
    opacity: 0;
  }

  /* Show sidebar when toggled */
  #wrapper.toggled #sidebar-wrapper {
    transform: translateX(0);
    visibility: visible;
    opacity: 1;
    box-shadow: var(--shadow-lg);
  }

  /* Overlay when sidebar is open */
  #wrapper.toggled::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  /* Page content takes full width on mobile */
  #page-content-wrapper {
    margin-left: 0;
    width: 100%;
  }

  /* Mobile bottom navigation */
  .mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--bottom-nav-height);
    background: var(--bg-primary);
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: var(--spacing-sm) 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1040;
    backdrop-filter: blur(10px);
  }

  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-secondary);
    transition: color 0.2s ease;
    padding: var(--spacing-xs);
    border-radius: 8px;
  }

  .mobile-nav-item.active,
  .mobile-nav-item:hover {
    color: var(--primary-color);
    background: var(--bg-secondary);
  }

  .mobile-nav-icon {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xs);
  }

  .mobile-nav-label {
    font-size: var(--font-xs);
    font-weight: 500;
  }

  /* Adjust main content for bottom nav */
  #page-content-wrapper {
    padding-bottom: calc(var(--bottom-nav-height) + var(--spacing-md));
  }

  /* Mobile-optimized dashboard grid */
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  /* Mobile card adjustments */
  .dashboard-card {
    padding: var(--spacing-md);
  }

  .dashboard-card-icon {
    width: 40px;
    height: 40px;
  }

  /* Mobile table improvements */
  .table-responsive {
    margin: 0 calc(-1 * var(--spacing-md));
    border-radius: 0;
  }

  .table th,
  .table td {
    padding: var(--spacing-sm);
    font-size: var(--font-sm);
  }

  /* Hide less important columns on mobile */
  .table .d-none-mobile {
    display: none;
  }

  /* Mobile form improvements */
  .form-control,
  .form-select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: var(--spacing-md);
  }

  /* Mobile button adjustments */
  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-base);
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }

  .btn-group .btn {
    width: auto;
    margin-bottom: 0;
  }

  /* Mobile typography */
  h1 { font-size: var(--font-2xl); }
  h2 { font-size: var(--font-xl); }
  h3 { font-size: var(--font-lg); }
  h4 { font-size: var(--font-base); }
  h5 { font-size: var(--font-sm); }
  h6 { font-size: var(--font-xs); }

  /* Mobile spacing adjustments */
  .container-fluid {
    padding: var(--spacing-sm);
  }

  /* Mobile navigation improvements */
  .top-navbar {
    padding: 0 var(--spacing-md);
  }

  .navbar-brand {
    font-size: var(--font-lg);
  }

  .navbar-toggler {
    padding: var(--spacing-sm);
    border: none;
    background: none;
  }

  /* Mobile dropdown improvements */
  .dropdown-menu {
    position: fixed !important;
    top: auto !important;
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
    max-width: 90% !important;
    width: 300px !important;
    margin-top: 10px !important;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
  }
}

/* Tablet-Specific Responsive Styles */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Sidebar behavior on tablet */
  #sidebar-wrapper {
    width: var(--sidebar-width-tablet);
  }

  #page-content-wrapper {
    margin-left: var(--sidebar-width-tablet);
  }

  /* Tablet grid layout */
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Hide mobile bottom nav on tablet */
  .mobile-bottom-nav {
    display: none;
  }

  /* Tablet table improvements */
  .table th,
  .table td {
    padding: var(--spacing-md);
  }

  /* Show some mobile-hidden columns on tablet */
  .table .d-none-mobile {
    display: table-cell;
  }

  .table .d-none-tablet {
    display: none;
  }
}

/* Desktop-Specific Responsive Styles */
@media (min-width: 1024px) {
  /* Always show sidebar on desktop */
  #sidebar-wrapper {
    transform: translateX(0);
    visibility: visible;
    opacity: 1;
  }

  /* Hide mobile elements */
  .mobile-bottom-nav {
    display: none;
  }

  .navbar-toggler {
    display: none;
  }

  /* Desktop grid layout */
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  /* Show all table columns on desktop */
  .table .d-none-mobile,
  .table .d-none-tablet {
    display: table-cell;
  }

  /* Desktop hover effects */
  .dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }

  .btn:hover {
    transform: translateY(-1px);
  }

  /* Desktop spacing */
  .container-fluid {
    padding: var(--spacing-xl);
  }
}

/* Print Styles */
@media print {
  #sidebar-wrapper,
  .top-navbar,
  .mobile-bottom-nav,
  .btn,
  .dropdown {
    display: none !important;
  }

  #page-content-wrapper {
    margin: 0;
    padding: 0;
    width: 100%;
  }

  .dashboard-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .table {
    font-size: 12px;
  }

  .table th,
  .table td {
    padding: 4px;
  }
}

/* High DPI / Retina Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .dashboard-card-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
